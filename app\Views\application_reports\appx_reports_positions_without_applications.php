<?php
/**
 * View file for Positions without Applications Report
 *
 * @var array $exercise Exercise details
 * @var array $positions List of positions without applications
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/application-register/' . $exercise['id']) ?>">Application Register</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Positions without Applications
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Positions without Applications</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/application-register/' . $exercise['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Application Register
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Alert: Positions Requiring Attention
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-6">
                            <div class="border rounded p-3">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning mb-1"><?= count($positions) ?></h4>
                                <p class="mb-0 text-muted">Positions without Applications</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="border rounded p-3">
                                <i class="fas fa-clock fa-2x text-danger mb-2"></i>
                                <h4 class="text-danger mb-1">Action Required</h4>
                                <p class="mb-0 text-muted">These positions need promotion or review</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Positions without Applications (<?= count($positions) ?> Positions)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($positions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-success">Excellent! All positions have applications</h5>
                            <p class="text-muted">Every position in this exercise has received at least one application.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="positionsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="30%">Position Details</th>
                                        <th width="15%">Position Group</th>
                                        <th width="15%">Organization</th>
                                        <th width="20%">Classification & Award</th>
                                        <th width="15%">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $count = 1; ?>
                                    <?php foreach ($positions as $position): ?>
                                        <tr>
                                            <td><?= $count++ ?></td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($position['designation']) ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        Ref: <?= esc($position['position_reference']) ?>
                                                    </small>
                                                    <br>
                                                    <small class="text-info">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?= esc($position['location']) ?>
                                                    </small>
                                                    <?php if (!empty($position['annual_salary'])): ?>
                                                        <br>
                                                        <small class="text-success">
                                                            <i class="fas fa-dollar-sign me-1"></i>
                                                            <?= esc($position['annual_salary']) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= esc($position['group_name']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= esc($position['org_name']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div>
                                                    <small class="d-block">
                                                        <strong>Class:</strong> <?= esc($position['classification']) ?>
                                                    </small>
                                                    <small class="d-block">
                                                        <strong>Award:</strong> <?= esc($position['award']) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    No Applications
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Recommendations Card -->
    <?php if (!empty($positions)): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        Recommendations
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Review position requirements and qualifications</li>
                                <li><i class="fas fa-check text-success me-2"></i>Consider extending application deadline</li>
                                <li><i class="fas fa-check text-success me-2"></i>Increase advertisement reach and visibility</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Review salary and benefits package</li>
                                <li><i class="fas fa-check text-success me-2"></i>Consider alternative recruitment channels</li>
                                <li><i class="fas fa-check text-success me-2"></i>Evaluate position location and requirements</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'asc']], // Sort by position name
        columnDefs: [
            { orderable: false, targets: [5] } // Disable sorting for Status column
        ]
    });
});
</script>
<?= $this->endSection() ?>
